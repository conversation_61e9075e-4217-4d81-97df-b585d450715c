import { useQuery } from '@tanstack/react-query';
import { callApi } from '../api/client';
import { TransferListPlayer, TransferListPlayersResponse } from './useTransferListPlayers';

export interface ActiveTransfer {
  id: string;
  date: number;
  player: {
    playerId: string;
    gameworldId: string;
    team: string;
    age: number;
    seed: number;
    firstName: string;
    surname: string;
    value: number;
    energy: number;
    lastMatchPlayed: number;
    injuredUntil: number | null;
    suspendedForGames: number;
    isTransferListed: boolean;
  };
  buyer: string;
  seller: {
    teamId: string;
    gameworldId: string;
    league: string;
    tier: number;
    teamName: string;
    managerId: string | null;
    balance: number;
    played: number;
    points: number;
    goalsFor: number;
    goalsAgainst: number;
    wins: number;
    draws: number;
    losses: number;
    selectionOrder: any[];
  };
  value: number;
  counterOfferTime: string;
  counterOfferValue: string;
}

export function useMyBidsPlayers(gameworldId?: string) {
  return useQuery<TransferListPlayersResponse>({
    queryKey: ['myBidsPlayers', gameworldId],
    queryFn: async () => {
      const endpoint = `/${gameworldId}/players/my-bids`;
      try {
        return await callApi(endpoint);
      } catch (error: any) {
        // Check if this is a "No players found" error (404)
        if (
          error.message?.includes('404') ||
          error.response?.status === 404 ||
          (typeof error === 'object' && error.error === 'No players found')
        ) {
          // Return an empty list instead of throwing an error
          return { players: [], lastEvaluatedKey: undefined };
        }

        // For other errors, rethrow
        throw error;
      }
    },
    enabled: !!gameworldId,
    refetchOnWindowFocus: false,
    retry: false,
  });
}

export function useMyActiveTransfers(gameworldId?: string) {
  return useQuery<ActiveTransfer[]>({
    queryKey: ['myActiveTransfers', gameworldId],
    queryFn: async () => {
      const endpoint = `/transfer/my-active`;
      try {
        return await callApi(endpoint);
      } catch (error: any) {
        // Check if this is a "No transfers found" error (404)
        if (
          error.message?.includes('404') ||
          error.response?.status === 404 ||
          (typeof error === 'object' && error.error === 'No transfers found')
        ) {
          // Return an empty list instead of throwing an error
          return [];
        }

        // For other errors, rethrow
        throw error;
      }
    },
    enabled: !!gameworldId,
    refetchOnWindowFocus: false,
    retry: false,
  });
}