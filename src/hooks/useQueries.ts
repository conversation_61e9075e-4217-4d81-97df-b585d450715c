import { useQuery } from '@tanstack/react-query';
import { callApi } from '../api/client';
import { DBFixture, Fixture } from '../models/fixture';
import { League } from '../models/league';
import { Manager } from '../models/manager';
import { Player } from '../models/player';
import { Team } from '../models/team';
import { logger } from '../utils/logger';

export interface ScoutedPlayer extends Player {
  lastScoutedAt: number;
}

export interface ScoutedPlayersResponse {
  scoutedPlayers: ScoutedPlayer[];
  pagination?: {
    hasMore: boolean;
    nextToken?: string;
  };
}

export function useFixtures(gameworldId?: string, leagueId?: string, teamId?: string) {
  return useQuery<Fixture[]>({
    queryKey: ['fixtures', gameworldId, leagueId, teamId],
    queryFn: async () => {
      const response = await callApi(`/${gameworldId}/league/${leagueId}/fixtures/${teamId}`);
      return response.fixtures;
    },
    enabled: !!gameworldId && !!leagueId && !!teamId,
  });
}

export function useCommentary() {
  return useQuery<Record<string, string>>({
    queryKey: ['commentary'],
    queryFn: () => callApi('/commentary'),
  });
}

export function useTeam(gameworldId?: string, teamId?: string) {
  return useQuery<Team>({
    queryKey: ['team', gameworldId, teamId],
    queryFn: () => callApi(`/${gameworldId}/team/${teamId}?includePlayers=true`),
    enabled: !!gameworldId && !!teamId,
  });
}

export function useFixtureDetails(gameworldId?: string, leagueId?: string, fixtureId?: string) {
  const fixtureQuery = useQuery<DBFixture>({
    queryKey: ['fixture', gameworldId, leagueId, fixtureId],
    queryFn: () => callApi(`/${gameworldId}/league/${leagueId}/fixture/${fixtureId}`),
    enabled: !!gameworldId && !!leagueId && !!fixtureId,
  });

  const commentaryQuery = useCommentary();

  const homeTeamQuery = useTeam(gameworldId, fixtureQuery.data?.homeTeam.teamId);

  const awayTeamQuery = useTeam(gameworldId, fixtureQuery.data?.awayTeam.teamId);

  return {
    data:
      fixtureQuery.data && commentaryQuery.data && homeTeamQuery.data && awayTeamQuery.data
        ? {
            fixture: fixtureQuery.data,
            commentary: commentaryQuery.data,
            homeTeamPlayers: homeTeamQuery.data.players,
            awayTeamPlayers: awayTeamQuery.data.players,
          }
        : undefined,
    isLoading:
      fixtureQuery.isLoading ||
      commentaryQuery.isLoading ||
      homeTeamQuery.isLoading ||
      awayTeamQuery.isLoading,
    error:
      fixtureQuery.error || commentaryQuery.error || homeTeamQuery.error || awayTeamQuery.error,
  };
}

export function useLeague(gameworldId?: string, leagueId?: string) {
  return useQuery<League>({
    queryKey: ['league', gameworldId, leagueId],
    queryFn: () => callApi(`/${gameworldId}/leagues/${leagueId}?includeTeams=true`),
    enabled: !!gameworldId && !!leagueId,
  });
}

export function useScoutedPlayers(gameworldId?: string, teamId?: string, nextToken?: string) {
  return useQuery<ScoutedPlayersResponse>({
    queryKey: ['scoutedPlayers', gameworldId, teamId, nextToken],
    queryFn: async () => {
      const endpoint = `/${gameworldId}/team/${teamId}/scouted-players${nextToken ? `?nextToken=${nextToken}` : ''}`;
      return await callApi(endpoint);
    },
    enabled: !!gameworldId && !!teamId,
  });
}

/**
 * Hook to fetch the manager data
 *
 * @returns Query result containing the manager data
 */
export function useManagerQuery() {
  return useQuery<Manager>({
    queryKey: ['manager'],
    queryFn: async () => {
      logger.log('Fetching manager data...');
      return await callApi('/manager');
    },
  });
}
