import { useQuery } from '@tanstack/react-query';
import { callApi } from '../api/client';
import { Player } from '../models/player';

export interface TransferListPlayer extends Player {
  auctionStartPrice: number;
  auctionCurrentPrice: number;
  auctionEndTime: number;
  bidHistory: { teamId: string; maximumBid: number; bidTime: number; teamName: string }[];
}

export interface TransferListPlayersResponse {
  players: TransferListPlayer[];
  pagination?: {
    lastEvaluatedKey?: string;
    nextToken?: string; // Alternative field name
    pageId?: string; // Another alternative field name
  };
  // Handle different response structures
  lastEvaluatedKey?: string;
  nextToken?: string;
  pageId?: string;
}

export function useTransferListPlayers(gameworldId?: string, lastEvaluatedKey?: string) {
  return useQuery<TransferListPlayersResponse>({
    queryKey: ['transferListPlayers', gameworldId, lastEvaluatedKey],
    queryFn: async () => {
      // Use the correct endpoint format with lastEvaluatedKey parameter
      // The API expects the parameter to be named 'lastEvaluatedKey'
      const endpoint = `/${gameworldId}/players/transfer-list${
        lastEvaluatedKey ? `?lastEvaluatedKey=${encodeURIComponent(lastEvaluatedKey)}` : ''
      }`;
      try {
        return await callApi(endpoint);
      } catch (error: any) {
        // Check if this is a "No players found" error (404)
        if (
          error.message?.includes('404') ||
          error.response?.status === 404 ||
          (typeof error === 'object' && error.error === 'No players found')
        ) {
          // Return an empty list instead of throwing an error
          return { players: [], lastEvaluatedKey: undefined };
        }

        // For other errors, rethrow
        throw error;
      }
    },
    enabled: !!gameworldId,
    // This ensures the query refetches when lastEvaluatedKey changes
    refetchOnWindowFocus: false,
    // Don't retry on error - we handle 404s gracefully above
    retry: false,
  });
}
