import { useQuery } from '@tanstack/react-query';
import { callApi } from '../api/client';

interface LeagueRules {
  promotion: number;
  relegation: number;
}

export interface LeagueSummary {
  gameworld: string;
  id: string;
  name: string;
  tier: number;
  rules: LeagueRules;
  parentLeagueId: string;
  childLeagueIds: string[];
}

export function useLeagues(gameworldId?: string) {
  return useQuery<LeagueSummary[]>({
    queryKey: ['leagues', gameworldId],
    queryFn: async () => {
      const response = await callApi(`/${gameworldId}/leagues`);
      return response.leagues;
    },
    enabled: !!gameworldId,
  });
}
