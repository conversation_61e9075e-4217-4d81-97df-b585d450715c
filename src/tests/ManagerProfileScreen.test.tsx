import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Alert, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeProvider } from 'styled-components/native';
import { callApi } from '../api/client';
import ManagerProfileScreen from '../screens/ManagerProfileScreen';
import { lightTheme } from '../theme/theme';
import * as ManagerContext from '../context/ManagerContext';

// Mock dependencies
jest.mock('../api/client', () => ({
  callApi: jest.fn(),
}));

jest.mock('../context/ManagerContext', () => ({
  useManager: jest.fn(),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
}));

jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useQueryClient: () => ({
    invalidateQueries: jest.fn(),
  }),
}));

// Mock Alert
jest.mock('react-native', () => {
  const rn = jest.requireActual('react-native');
  return {
    ...rn,
    Alert: {
      ...rn.Alert,
      alert: jest.fn(),
    },
    Platform: {
      ...rn.Platform,
      OS: 'ios',
    },
  };
});

describe('ManagerProfileScreen', () => {
  const queryClient = new QueryClient();

  // Setup mock data
  const mockManager = {
    managerId: 'manager-123',
    firstName: 'John',
    lastName: 'Doe',
    createdAt: Date.now(),
    lastActive: Date.now(),
    scoutTokens: 5,
    superScoutTokens: 2,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useManager hook
    (ManagerContext.useManager as jest.Mock).mockReturnValue({
      manager: mockManager,
      loading: false,
    });

    // Mock callApi
    (callApi as jest.Mock).mockResolvedValue({});
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={lightTheme}>
          <ManagerProfileScreen />
        </ThemeProvider>
      </QueryClientProvider>
    );
  };

  it('renders correctly with manager data', () => {
    const { getByText, getByPlaceholderText } = renderComponent();

    expect(getByText('First Name')).toBeTruthy();
    expect(getByText('Last Name')).toBeTruthy();
    expect(getByPlaceholderText('Enter your first name').props.value).toBe('John');
    expect(getByPlaceholderText('Enter your last name').props.value).toBe('Doe');
    expect(getByText('Save Profile')).toBeTruthy();
  });

  it('loads cached data when manager is not available', async () => {
    // Mock manager as null
    (ManagerContext.useManager as jest.Mock).mockReturnValue({
      manager: null,
      loading: false,
    });

    // Mock AsyncStorage to return cached values
    (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
      if (key === '@cached_first_name') return Promise.resolve('Cached');
      if (key === '@cached_last_name') return Promise.resolve('Name');
      return Promise.resolve(null);
    });

    const { getByPlaceholderText } = renderComponent();

    // Wait for the async operations to complete
    await waitFor(() => {
      expect(getByPlaceholderText('Enter your first name').props.value).toBe('Cached');
      expect(getByPlaceholderText('Enter your last name').props.value).toBe('Name');
    });
  });

  it('shows loading indicator when loading', () => {
    (ManagerContext.useManager as jest.Mock).mockReturnValue({
      manager: null,
      loading: true,
    });

    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={lightTheme}>
          <ManagerProfileScreen />
        </ThemeProvider>
      </QueryClientProvider>
    );

    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

  it('updates profile when save button is pressed with manager available', async () => {
    const { getByText, getByPlaceholderText } = renderComponent();

    // Change input values
    fireEvent.changeText(getByPlaceholderText('Enter your first name'), 'Jane');
    fireEvent.changeText(getByPlaceholderText('Enter your last name'), 'Smith');

    // Press save button
    fireEvent.press(getByText('Save Profile'));

    // Verify API call
    await waitFor(() => {
      expect(callApi).toHaveBeenCalledWith('/manager/name', {
        method: 'PUT',
        body: JSON.stringify({
          firstName: 'Jane',
          lastName: 'Smith',
        }),
      });
    });

    // Verify AsyncStorage calls
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@cached_first_name', 'Jane');
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@cached_last_name', 'Smith');
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@profile_needs_update', 'false');
  });

  it('caches profile when save button is pressed with no manager available', async () => {
    // Mock manager as null (not assigned yet)
    (ManagerContext.useManager as jest.Mock).mockReturnValue({
      manager: null,
      loading: false,
    });

    const { getByText, getByPlaceholderText } = renderComponent();

    // Change input values
    fireEvent.changeText(getByPlaceholderText('Enter your first name'), 'Jane');
    fireEvent.changeText(getByPlaceholderText('Enter your last name'), 'Smith');

    // Press save button
    fireEvent.press(getByText('Save Profile'));

    // Verify API call was not made
    await waitFor(() => {
      expect(callApi).not.toHaveBeenCalled();
    });

    // Verify AsyncStorage calls
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@cached_first_name', 'Jane');
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@cached_last_name', 'Smith');
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@profile_needs_update', 'true');
  });

  it('shows error when fields are empty', async () => {
    const { getByText, getByPlaceholderText } = renderComponent();

    // Clear input values
    fireEvent.changeText(getByPlaceholderText('Enter your first name'), '');
    fireEvent.changeText(getByPlaceholderText('Enter your last name'), '');

    // Press save button
    fireEvent.press(getByText('Save Profile'));

    // Verify API call was not made
    await waitFor(() => {
      expect(callApi).not.toHaveBeenCalled();
    });

    // Check if Alert was called (on native platforms)
    if (Platform.OS !== 'web') {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Please enter both first and last name');
    }
  });

  it('auto-saves profile when manager becomes available', async () => {
    // First mock the AsyncStorage to return cached values and update flag
    (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
      if (key === '@cached_first_name') return Promise.resolve('Jane');
      if (key === '@cached_last_name') return Promise.resolve('Smith');
      if (key === '@profile_needs_update') return Promise.resolve('true');
      return Promise.resolve(null);
    });

    // Initially render with no manager
    (ManagerContext.useManager as jest.Mock).mockReturnValue({
      manager: null,
      loading: false,
    });

    const { rerender } = renderComponent();

    // Then update to have a manager (simulating manager assignment)
    (ManagerContext.useManager as jest.Mock).mockReturnValue({
      manager: {
        ...mockManager,
        firstName: undefined, // These should be updated from cache
        lastName: undefined,
      },
      loading: false,
    });

    // Re-render with the new manager
    rerender(
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={lightTheme}>
          <ManagerProfileScreen />
        </ThemeProvider>
      </QueryClientProvider>
    );

    // Verify the API was called with the cached values
    await waitFor(() => {
      expect(callApi).toHaveBeenCalledWith('/manager/name', {
        method: 'PUT',
        body: JSON.stringify({
          firstName: 'Jane',
          lastName: 'Smith',
        }),
      });
    });

    // Verify the flag was cleared
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('@profile_needs_update', 'false');
  });
});
