import { render } from '@testing-library/react-native';
import React from 'react';
import { ThemeProvider } from '../theme/ThemeContext';
import TrainingScreen from '../screens/TrainingScreen';

// Mock the context and hooks
jest.mock('../context/ManagerContext', () => ({
  useManager: () => ({
    team: {
      teamName: 'Test Team',
      balance: 1000000,
      players: [
        {
          playerId: 'player-1',
          firstName: '<PERSON>',
          surname: '<PERSON><PERSON>',
          attributes: {
            reflexes: 70,
            positioning: 75,
            shotStopping: 80,
            tackling: 65,
            marking: 70,
            heading: 75,
            finishing: 60,
            pace: 80,
            crossing: 70,
            passing: 75,
            vision: 80,
            ballControl: 85,
            stamina: 90,
          },
        },
      ],
    },
    loading: false,
    manager: { gameworldId: 'test-world', team: 'test-team' },
  }),
}));

jest.mock('../hooks/useQueries', () => ({
  useTeam: () => ({
    data: {
      players: [
        {
          playerId: 'player-1',
          firstName: '<PERSON>',
          surname: '<PERSON><PERSON>',
          attributes: {
            reflexes: 70,
            positioning: 75,
            shotStopping: 80,
            tackling: 65,
            marking: 70,
            heading: 75,
            finishing: 60,
            pace: 80,
            crossing: 70,
            passing: 75,
            vision: 80,
            ballControl: 85,
            stamina: 90,
          },
        },
      ],
    },
  }),
}));

describe('TrainingScreen', () => {
  it('renders training ground header', () => {
    const { getByText } = render(
      <ThemeProvider>
        <TrainingScreen />
      </ThemeProvider>
    );

    expect(getByText('Training Ground')).toBeTruthy();
    expect(getByText('Training Ground Quality')).toBeTruthy();
    expect(getByText('Level 1')).toBeTruthy();
  });

  it('renders 5 training slots', () => {
    const { getByText } = render(
      <ThemeProvider>
        <TrainingScreen />
      </ThemeProvider>
    );

    expect(getByText('Training Slot 1')).toBeTruthy();
    expect(getByText('Training Slot 2')).toBeTruthy();
    expect(getByText('Training Slot 3')).toBeTruthy();
    expect(getByText('Training Slot 4')).toBeTruthy();
    expect(getByText('Training Slot 5')).toBeTruthy();
  });

  it('shows first slot as unlocked', () => {
    const { getByText } = render(
      <ThemeProvider>
        <TrainingScreen />
      </ThemeProvider>
    );

    expect(getByText('Tap to assign a player')).toBeTruthy();
  });

  it('shows unlock costs for locked slots', () => {
    const { getByText } = render(
      <ThemeProvider>
        <TrainingScreen />
      </ThemeProvider>
    );

    expect(getByText('Unlock for £50K')).toBeTruthy();
    expect(getByText('Unlock for £100K')).toBeTruthy();
    expect(getByText('Unlock for £200K')).toBeTruthy();
    expect(getByText('Unlock for £500K')).toBeTruthy();
  });
});
