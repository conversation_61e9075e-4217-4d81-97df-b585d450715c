import AsyncStorage from '@react-native-async-storage/async-storage';
import { renderHook, waitFor } from '@testing-library/react-native';
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ManagerProvider, useManager } from '../context/ManagerContext';
import * as useQueriesModule from '../hooks/useQueries';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
}));

// Create a wrapper for the hooks
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ManagerProvider>{children}</ManagerProvider>
    </QueryClientProvider>
  );
};

describe('ManagerContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should save manager and team IDs to AsyncStorage when data is loaded', async () => {
    // Mock the manager data
    const mockManager = {
      managerId: 'manager-123',
      team: 'team-456',
      gameworldId: 'gameworld-789',
      createdAt: Date.now(),
      lastActive: Date.now(),
      scoutTokens: 5,
      superScoutTokens: 2,
    };

    // Mock the team data
    const mockTeam = {
      gameworldId: 'gameworld-789',
      league: { id: 'league-123' },
      teamId: 'team-456',
      teamName: 'Test Team',
      players: [],
      played: 0,
      wins: 0,
      draws: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      losses: 0,
      points: 0,
    };

    // Mock the hooks
    jest.spyOn(useQueriesModule, 'useManagerData').mockReturnValue({
      data: mockManager,
      isLoading: false,
      error: null,
    } as any);

    jest.spyOn(useQueriesModule, 'useTeam').mockReturnValue({
      data: mockTeam,
      isLoading: false,
      error: null,
    } as any);

    // Render the hook
    renderHook(() => useManager(), { wrapper: createWrapper() });

    // Wait for AsyncStorage to be called
    await waitFor(() => {
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('@manager_id', 'manager-123');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('@team_id', 'team-456');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('@gameworld_id', 'gameworld-789');
    });
  });

  it('should load stored IDs from AsyncStorage on mount', async () => {
    // Mock AsyncStorage.getItem to return stored values
    (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
      if (key === '@manager_id') return Promise.resolve('stored-manager-id');
      if (key === '@team_id') return Promise.resolve('stored-team-id');
      if (key === '@gameworld_id') return Promise.resolve('stored-gameworld-id');
      return Promise.resolve(null);
    });

    // Mock the hooks to return null data (simulating loading state)
    jest.spyOn(useQueriesModule, 'useManagerData').mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    } as any);

    jest.spyOn(useQueriesModule, 'useTeam').mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    } as any);

    // Render the hook
    const { result } = renderHook(() => useManager(), { wrapper: createWrapper() });

    // Wait for the stored IDs to be loaded
    await waitFor(() => {
      expect(result.current.storedIds).toEqual({
        managerId: 'stored-manager-id',
        teamId: 'stored-team-id',
        gameworldId: 'stored-gameworld-id',
      });
    });
  });
});
