import { callApi } from '../api/client';

// Mock the API client
jest.mock('../api/client', () => ({
  callApi: jest.fn(),
}));

const mockCallApi = callApi as jest.MockedFunction<typeof callApi>;

describe('Release Player API Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call the release player API with correct parameters', async () => {
    // Mock successful API response
    const mockResponse = {
      message: 'Player released successfully',
      playerId: 'player-123',
      playerName: '<PERSON>',
    };
    
    mockCallApi.mockResolvedValueOnce(mockResponse);

    // Test data
    const playerId = 'player-123';
    const teamId = 'team-456';

    // Make the API call
    const response = await callApi('/transfer/release-player', {
      method: 'POST',
      body: JSON.stringify({
        playerId,
        teamId,
      }),
    });

    // Verify the API was called with correct parameters
    expect(mockCallApi).toHaveBeenCalledWith('/transfer/release-player', {
      method: 'POST',
      body: JSON.stringify({
        playerId: 'player-123',
        teamId: 'team-456',
      }),
    });

    // Verify the response
    expect(response).toEqual(mockResponse);
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    const apiError = new Error('Failed to release player');
    mockCallApi.mockRejectedValueOnce(apiError);

    // Test data
    const playerId = 'player-123';
    const teamId = 'team-456';

    // Expect the API call to throw
    await expect(
      callApi('/transfer/release-player', {
        method: 'POST',
        body: JSON.stringify({
          playerId,
          teamId,
        }),
      })
    ).rejects.toThrow('Failed to release player');

    // Verify the API was called
    expect(mockCallApi).toHaveBeenCalledWith('/transfer/release-player', {
      method: 'POST',
      body: JSON.stringify({
        playerId: 'player-123',
        teamId: 'team-456',
      }),
    });
  });

  it('should use the correct request format for release player', () => {
    const playerId = 'test-player-123';
    const teamId = 'test-team-456';
    
    const expectedRequestBody = {
      playerId,
      teamId,
    };

    // Verify the request format matches the API specification
    expect(JSON.stringify(expectedRequestBody)).toBe(
      JSON.stringify({ playerId, teamId })
    );
  });

  it('should validate required parameters', () => {
    const playerId = 'player-123';
    const teamId = 'team-456';

    // Both parameters should be required
    expect(playerId).toBeTruthy();
    expect(teamId).toBeTruthy();
    
    // Parameters should be strings
    expect(typeof playerId).toBe('string');
    expect(typeof teamId).toBe('string');
  });

  it('should handle successful response with expected structure', async () => {
    const mockResponse = {
      message: 'Player released successfully',
      playerId: 'player-123',
      playerName: 'John Doe',
    };
    
    mockCallApi.mockResolvedValueOnce(mockResponse);

    const response = await callApi('/transfer/release-player', {
      method: 'POST',
      body: JSON.stringify({
        playerId: 'player-123',
        teamId: 'team-456',
      }),
    });

    // Verify response structure
    expect(response).toHaveProperty('message');
    expect(response).toHaveProperty('playerId');
    expect(response).toHaveProperty('playerName');
    
    // Verify response values
    expect(response.message).toBe('Player released successfully');
    expect(response.playerId).toBe('player-123');
    expect(response.playerName).toBe('John Doe');
  });
});
