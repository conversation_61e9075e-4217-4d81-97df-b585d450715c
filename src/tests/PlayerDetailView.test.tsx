import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import PlayerDetailView from '../components/PlayerDetailView';
import { ManagerProvider } from '../context/ManagerContext';
import { ThemeProvider } from '../theme/ThemeContext';
import * as apiClient from '../api/client';
import * as useQueriesModule from '../hooks/useQueries';

// Mock the API client
jest.mock('../api/client', () => ({
  callApi: jest.fn(),
}));

// Mock the hooks
jest.mock('../hooks/useQueries', () => ({
  useManagerQuery: jest.fn(),
  useTeam: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
}));

// Create a wrapper for the components
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <ManagerProvider>{children}</ManagerProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

const mockPlayer = {
  playerId: 'player-123',
  firstName: 'John',
  surname: 'Doe',
  teamId: 'team-456',
  age: 25,
  value: 1000000,
  energy: 100,
  lastMatchPlayed: Date.now() - 86400000, // 1 day ago
  gameworldId: 'gameworld-789',
  attributes: {
    reflexes: 70,
    positioning: 75,
    shotStopping: 80,
    tackling: 65,
    marking: 70,
    heading: 75,
    finishing: 60,
    pace: 80,
    crossing: 70,
    passing: 75,
    vision: 80,
    ballControl: 85,
    stamina: 90,
  },
  injuredUntil: undefined,
  suspendedForGames: 0,
};

const mockTeam = {
  teamId: 'team-456',
  teamName: 'Test Team',
  gameworldId: 'gameworld-789',
  league: { id: 'league-123' },
  nextFixture: { fixtureId: 'fixture-123', date: Date.now() + 86400000 },
  players: [mockPlayer],
  balance: 5000000,
  played: 10,
  wins: 6,
  draws: 2,
  losses: 2,
  goalsFor: 18,
  goalsAgainst: 12,
  points: 20,
};

const mockManager = {
  managerId: 'manager-123',
  team: 'team-456',
  gameworldId: 'gameworld-789',
  createdAt: Date.now(),
  lastActive: Date.now(),
  scoutTokens: 5,
  superScoutTokens: 2,
};

describe('PlayerDetailView Release Player Feature', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the manager query
    (useQueriesModule.useManagerQuery as jest.Mock).mockReturnValue({
      data: mockManager,
      isLoading: false,
      error: null,
    });

    // Mock the team query
    (useQueriesModule.useTeam as jest.Mock).mockReturnValue({
      data: mockTeam,
      isLoading: false,
      error: null,
    });
  });

  it('should show release button for players in user team', () => {
    const onClose = jest.fn();
    
    const { getByText } = render(
      <PlayerDetailView 
        player={mockPlayer} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    expect(getByText('Release Player')).toBeTruthy();
  });

  it('should not show release button for players not in user team', () => {
    const onClose = jest.fn();
    const playerFromOtherTeam = { ...mockPlayer, teamId: 'other-team-123' };
    
    const { queryByText } = render(
      <PlayerDetailView 
        player={playerFromOtherTeam} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    expect(queryByText('Release Player')).toBeNull();
  });

  it('should show confirmation modal when release button is pressed', () => {
    const onClose = jest.fn();
    
    const { getByText } = render(
      <PlayerDetailView 
        player={mockPlayer} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    fireEvent.press(getByText('Release Player'));

    expect(getByText('Are you sure you want to release John Doe?')).toBeTruthy();
    expect(getByText('This action cannot be undone. The player will be removed from your team permanently.')).toBeTruthy();
  });

  it('should call release API when confirmed', async () => {
    const mockApiResponse = {
      message: 'Player released successfully',
      playerId: 'player-123',
      playerName: 'John Doe',
    };
    
    (apiClient.callApi as jest.Mock).mockResolvedValue(mockApiResponse);
    
    const onClose = jest.fn();
    
    const { getByText } = render(
      <PlayerDetailView 
        player={mockPlayer} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    // Open release modal
    fireEvent.press(getByText('Release Player'));
    
    // Confirm release
    fireEvent.press(getByText('Release Player'));

    await waitFor(() => {
      expect(apiClient.callApi).toHaveBeenCalledWith('/transfer/release-player', {
        method: 'POST',
        body: JSON.stringify({
          playerId: 'player-123',
          teamId: 'team-456',
        }),
      });
    });
  });

  it('should show success message after successful release', async () => {
    const mockApiResponse = {
      message: 'Player released successfully',
      playerId: 'player-123',
      playerName: 'John Doe',
    };
    
    (apiClient.callApi as jest.Mock).mockResolvedValue(mockApiResponse);
    
    const onClose = jest.fn();
    
    const { getByText } = render(
      <PlayerDetailView 
        player={mockPlayer} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    // Open release modal and confirm
    fireEvent.press(getByText('Release Player'));
    fireEvent.press(getByText('Release Player'));

    await waitFor(() => {
      expect(getByText('Success')).toBeTruthy();
      expect(getByText('Player released successfully')).toBeTruthy();
    });
  });

  it('should show error message when release fails', async () => {
    (apiClient.callApi as jest.Mock).mockRejectedValue(new Error('API Error'));
    
    const onClose = jest.fn();
    
    const { getByText } = render(
      <PlayerDetailView 
        player={mockPlayer} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    // Open release modal and confirm
    fireEvent.press(getByText('Release Player'));
    fireEvent.press(getByText('Release Player'));

    await waitFor(() => {
      expect(getByText('Error')).toBeTruthy();
      expect(getByText('Failed to release player. Please try again.')).toBeTruthy();
    });
  });

  it('should close modal when cancel is pressed', () => {
    const onClose = jest.fn();
    
    const { getByText, queryByText } = render(
      <PlayerDetailView 
        player={mockPlayer} 
        team={mockTeam} 
        onClose={onClose} 
      />,
      { wrapper: createWrapper() }
    );

    // Open release modal
    fireEvent.press(getByText('Release Player'));
    expect(getByText('Are you sure you want to release John Doe?')).toBeTruthy();
    
    // Press cancel
    fireEvent.press(getByText('Cancel'));
    
    // Modal should be closed
    expect(queryByText('Are you sure you want to release John Doe?')).toBeNull();
  });
});
