import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { Manager } from '../models/manager';
import { Player } from '../models/player';
import { Team } from '../models/team';
import { TransferListPlayer } from '../hooks/useTransferListPlayers';
import { ScoutedPlayer } from '../hooks/useQueries';
import {
  DataCache,
  createEmptyCache,
  updatePlayerInCache,
  updateManagerInCache,
  updateTeamInCache,
  findPlayerInCache,
  CachedPlayerData,
} from '../utils/cacheUtils';
import { logger } from '../utils/logger';

// Action types for cache reducer
type CacheAction =
  | { type: 'SET_MANAGER'; payload: Manager }
  | { type: 'UPDATE_MANAGER'; payload: Partial<Manager> }
  | { type: 'SET_TEAM'; payload: Team }
  | { type: 'UPDATE_TEAM'; payload: Partial<Team> }
  | { type: 'SET_TEAM_PLAYERS'; payload: Player[] }
  | { type: 'SET_TRANSFER_LIST_PLAYERS'; payload: TransferListPlayer[] }
  | { type: 'ADD_TRANSFER_LIST_PLAYERS'; payload: TransferListPlayer[] }
  | { type: 'SET_SCOUTED_PLAYERS'; payload: ScoutedPlayer[] }
  | { type: 'ADD_SCOUTED_PLAYERS'; payload: ScoutedPlayer[] }
  | { type: 'SET_MY_BIDS_PLAYERS'; payload: TransferListPlayer[] }
  | { type: 'UPDATE_PLAYER'; payload: Partial<Player> & { playerId: string } }
  | { type: 'CLEAR_CACHE' };

// Cache reducer
const cacheReducer = (state: DataCache, action: CacheAction): DataCache => {
  switch (action.type) {
    case 'SET_MANAGER':
      return {
        ...state,
        manager: action.payload,
        lastUpdated: { ...state.lastUpdated, manager: Date.now() },
      };

    case 'UPDATE_MANAGER':
      return updateManagerInCache(state, action.payload);

    case 'SET_TEAM':
      return {
        ...state,
        team: action.payload,
        lastUpdated: { ...state.lastUpdated, team: Date.now() },
      };

    case 'UPDATE_TEAM':
      return updateTeamInCache(state, action.payload);

    case 'SET_TEAM_PLAYERS':
      return {
        ...state,
        players: { ...state.players, teamPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_TRANSFER_LIST_PLAYERS':
      return {
        ...state,
        players: { ...state.players, transferListPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'ADD_TRANSFER_LIST_PLAYERS':
      // Filter out duplicates before adding
      const existingTransferIds = new Set(state.players.transferListPlayers.map(p => p.playerId));
      const newTransferPlayers = action.payload.filter(p => !existingTransferIds.has(p.playerId));
      return {
        ...state,
        players: {
          ...state.players,
          transferListPlayers: [...state.players.transferListPlayers, ...newTransferPlayers],
        },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_SCOUTED_PLAYERS':
      return {
        ...state,
        players: { ...state.players, scoutedPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'ADD_SCOUTED_PLAYERS':
      // Filter out duplicates before adding
      const existingScoutedIds = new Set(state.players.scoutedPlayers.map(p => p.playerId));
      const newScoutedPlayers = action.payload.filter(p => !existingScoutedIds.has(p.playerId));
      return {
        ...state,
        players: {
          ...state.players,
          scoutedPlayers: [...state.players.scoutedPlayers, ...newScoutedPlayers],
        },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_MY_BIDS_PLAYERS':
      return {
        ...state,
        players: { ...state.players, myBidsPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'UPDATE_PLAYER':
      return updatePlayerInCache(state, action.payload);

    case 'CLEAR_CACHE':
      return createEmptyCache();

    default:
      return state;
  }
};

interface DataCacheContextType {
  cache: DataCache;
  // Manager actions
  setManager: (manager: Manager) => void;
  updateManager: (updates: Partial<Manager>) => void;
  // Team actions
  setTeam: (team: Team) => void;
  updateTeam: (updates: Partial<Team>) => void;
  // Player actions
  setTeamPlayers: (players: Player[]) => void;
  setTransferListPlayers: (players: TransferListPlayer[]) => void;
  addTransferListPlayers: (players: TransferListPlayer[]) => void;
  setScoutedPlayers: (players: ScoutedPlayer[]) => void;
  addScoutedPlayers: (players: ScoutedPlayer[]) => void;
  setMyBidsPlayers: (players: TransferListPlayer[]) => void;
  updatePlayer: (updates: Partial<Player> & { playerId: string }) => void;
  // Utility functions
  findPlayer: (playerId: string) => Player | TransferListPlayer | ScoutedPlayer | null;
  clearCache: () => void;
}

const DataCacheContext = createContext<DataCacheContextType | undefined>(undefined);

export const DataCacheProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cache, dispatch] = useReducer(cacheReducer, createEmptyCache());

  // Manager actions
  const setManager = useCallback((manager: Manager) => {
    logger.log('Cache: Setting manager data');
    dispatch({ type: 'SET_MANAGER', payload: manager });
  }, []);

  const updateManager = useCallback((updates: Partial<Manager>) => {
    logger.log('Cache: Updating manager data', updates);
    dispatch({ type: 'UPDATE_MANAGER', payload: updates });
  }, []);

  // Team actions
  const setTeam = useCallback((team: Team) => {
    logger.log('Cache: Setting team data');
    dispatch({ type: 'SET_TEAM', payload: team });
  }, []);

  const updateTeam = useCallback((updates: Partial<Team>) => {
    logger.log('Cache: Updating team data', updates);
    dispatch({ type: 'UPDATE_TEAM', payload: updates });
  }, []);

  // Player actions
  const setTeamPlayers = useCallback((players: Player[]) => {
    logger.log('Cache: Setting team players', players.length);
    dispatch({ type: 'SET_TEAM_PLAYERS', payload: players });
  }, []);

  const setTransferListPlayers = useCallback((players: TransferListPlayer[]) => {
    logger.log('Cache: Setting transfer list players', players.length);
    dispatch({ type: 'SET_TRANSFER_LIST_PLAYERS', payload: players });
  }, []);

  const addTransferListPlayers = useCallback((players: TransferListPlayer[]) => {
    logger.log('Cache: Adding transfer list players', players.length);
    dispatch({ type: 'ADD_TRANSFER_LIST_PLAYERS', payload: players });
  }, []);

  const setScoutedPlayers = useCallback((players: ScoutedPlayer[]) => {
    logger.log('Cache: Setting scouted players', players.length);
    dispatch({ type: 'SET_SCOUTED_PLAYERS', payload: players });
  }, []);

  const addScoutedPlayers = useCallback((players: ScoutedPlayer[]) => {
    logger.log('Cache: Adding scouted players', players.length);
    dispatch({ type: 'ADD_SCOUTED_PLAYERS', payload: players });
  }, []);

  const setMyBidsPlayers = useCallback((players: TransferListPlayer[]) => {
    logger.log('Cache: Setting my bids players', players.length);
    dispatch({ type: 'SET_MY_BIDS_PLAYERS', payload: players });
  }, []);

  const updatePlayer = useCallback((updates: Partial<Player> & { playerId: string }) => {
    logger.log('Cache: Updating player', updates.playerId, updates);
    dispatch({ type: 'UPDATE_PLAYER', payload: updates });
  }, []);

  // Utility functions
  const findPlayer = useCallback((playerId: string) => {
    return findPlayerInCache(cache, playerId);
  }, [cache]);

  const clearCache = useCallback(() => {
    logger.log('Cache: Clearing all cache data');
    dispatch({ type: 'CLEAR_CACHE' });
  }, []);

  return (
    <DataCacheContext.Provider
      value={{
        cache,
        setManager,
        updateManager,
        setTeam,
        updateTeam,
        setTeamPlayers,
        setTransferListPlayers,
        addTransferListPlayers,
        setScoutedPlayers,
        addScoutedPlayers,
        setMyBidsPlayers,
        updatePlayer,
        findPlayer,
        clearCache,
      }}
    >
      {children}
    </DataCacheContext.Provider>
  );
};

export const useDataCache = () => {
  const context = useContext(DataCacheContext);
  if (context === undefined) {
    throw new Error('useDataCache must be used within a DataCacheProvider');
  }
  return context;
};
