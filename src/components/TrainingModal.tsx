import React, { useState } from 'react';
import { FlatList, Modal, Platform, ScrollView, TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Player } from '../models/player';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface TrainingModalProps {
  visible: boolean;
  players: Player[];
  onClose: () => void;
  onAssign: (player: Player, attribute: string) => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-height: 70%;
  overflow: visible;
  flex: 1;
`;

const ModalTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 20px;
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 10px;
  margin-top: 10px;
`;

const ListContainer = styled.View<StyledProps>`
  flex: 1;
  margin: 16px 0;
`;

const ListItem = styled(TouchableOpacity)<StyledProps>`
  padding: 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.background};
  margin-bottom: 8px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.text.secondary};
`;

const ListItemText = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.primary};
`;

const AttributeItemText = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const AttributeCategoryText = styled(Text)<StyledProps>`
  font-size: 14px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.secondary};
`;

const StepIndicator = styled.View`
  flex-direction: row;
  justify-content: center;
  margin-bottom: 20px;
`;

const StepDot = styled.View<StyledProps & { active: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: ${(props) =>
    props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};
  margin: 0 4px;
`;

const SelectedInfo = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background};
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border-left-width: 4px;
  border-left-color: ${(props) => props.theme.colors.primary};
`;

const SelectedInfoText = styled(Text)<StyledProps>`
  font-size: 14px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
`;

const AssistantMessage = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background};
  border-radius: 8px;
  padding: 12px;
  margin: 16px 0;
  border-left-width: 4px;
  border-left-color: ${(props) => props.theme.colors.primary};
`;

const AssistantMessageText = styled(Text)<StyledProps>`
  font-size: 14px;
  font-family: 'Nunito';
  color: ${(props) => props.theme.colors.text.primary};
  line-height: 20px;
  font-style: italic;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
`;

const Button = styled(TouchableOpacity)<StyledProps>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.primary};
  margin: 0 5px;
`;

const ButtonText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.surface};
  font-family: 'NunitoBold';
  text-align: center;
`;

const TrainingModal: React.FC<TrainingModalProps> = ({ visible, players, onClose, onAssign }) => {
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [selectedAttribute, setSelectedAttribute] = useState<string | null>(null);
  const [step, setStep] = useState<'player' | 'attribute' | 'confirm'>('player');

  const attributes = [
    { key: 'reflexes', name: 'Reflexes', category: 'Goalkeeper' },
    { key: 'positioning', name: 'Positioning', category: 'Goalkeeper' },
    { key: 'shotStopping', name: 'Shot Stopping', category: 'Goalkeeper' },
    { key: 'tackling', name: 'Tackling', category: 'Defender' },
    { key: 'marking', name: 'Marking', category: 'Defender' },
    { key: 'heading', name: 'Heading', category: 'Defender' },
    { key: 'finishing', name: 'Finishing', category: 'Attacker' },
    { key: 'pace', name: 'Pace', category: 'Attacker' },
    { key: 'crossing', name: 'Crossing', category: 'Attacker' },
    { key: 'passing', name: 'Passing', category: 'Midfielder' },
    { key: 'vision', name: 'Vision', category: 'Midfielder' },
    { key: 'ballControl', name: 'Ball Control', category: 'Midfielder' },
    { key: 'stamina', name: 'Stamina', category: 'General' },
  ];

  const getAssistantMessage = (player: Player): string => {
    const potentialText =
      player.trainingPotential > 0.8
        ? 'has exceptional potential for improvement'
        : player.trainingPotential > 0.6
          ? 'has good potential for development'
          : player.trainingPotential > 0.3
            ? 'has moderate potential for growth'
            : player.trainingPotential > 0.1
              ? 'has limited potential remaining'
              : 'has reached their peak and may not improve much further';

    const topAreas =
      player.topTrainingAreas && player.topTrainingAreas.length > 0
        ? player.topTrainingAreas.slice(0, 3).join(', ')
        : 'general fitness';

    return `Assistant Manager: "${player.firstName} ${potentialText}. I'd recommend focusing on ${topAreas} for the best results."`;
  };

  const handlePlayerSelect = (player: Player) => {
    setSelectedPlayer(player);
    setStep('attribute');
  };

  const handleAttributeSelect = (attribute: string) => {
    setSelectedAttribute(attribute);
    setStep('confirm');
  };

  const handleAssign = () => {
    if (selectedPlayer && selectedAttribute) {
      onAssign(selectedPlayer, selectedAttribute);
      handleClose();
    }
  };

  const handleBack = () => {
    if (step === 'attribute') {
      setStep('player');
      setSelectedAttribute(null);
    } else if (step === 'confirm') {
      setStep('attribute');
    }
  };

  const handleClose = () => {
    setSelectedPlayer(null);
    setSelectedAttribute(null);
    setStep('player');
    onClose();
  };

  const getStepTitle = () => {
    switch (step) {
      case 'player':
        return 'Select Player';
      case 'attribute':
        return 'Select Training Attribute';
      case 'confirm':
        return 'Confirm Assignment';
      default:
        return 'Assign Player to Training';
    }
  };

  const renderStepContent = () => {
    console.log('renderStepContent', step);
    switch (step) {
      case 'player':
        return (
          <ListContainer key={'playerStepContainer'}>
            <Text>{step}</Text>
            <FlatList
              data={players}
              keyExtractor={(item) => item.playerId}
              showsVerticalScrollIndicator={true}
              renderItem={({ item: player }) => (
                <ListItem onPress={() => handlePlayerSelect(player)}>
                  <ListItemText>
                    {player.firstName} {player.surname}
                  </ListItemText>
                </ListItem>
              )}
            />
          </ListContainer>
        );

      case 'attribute':
        return (
          <>
            {selectedPlayer && (
              <SelectedInfo>
                <SelectedInfoText>
                  Selected: {selectedPlayer.firstName} {selectedPlayer.surname}
                </SelectedInfoText>
              </SelectedInfo>
            )}

            {selectedPlayer && (
              <AssistantMessage>
                <AssistantMessageText>{getAssistantMessage(selectedPlayer)}</AssistantMessageText>
              </AssistantMessage>
            )}

            <ListContainer>
              <FlatList
                data={attributes}
                keyExtractor={(item) => item.key}
                showsVerticalScrollIndicator={true}
                renderItem={({ item: attr }) => (
                  <ListItem onPress={() => handleAttributeSelect(attr.key)}>
                    <AttributeItemText>{attr.name}</AttributeItemText>
                    <AttributeCategoryText>{attr.category}</AttributeCategoryText>
                  </ListItem>
                )}
              />
            </ListContainer>
          </>
        );

      case 'confirm':
        return (
          <>
            {selectedPlayer && (
              <SelectedInfo>
                <SelectedInfoText>
                  Player: {selectedPlayer.firstName} {selectedPlayer.surname}
                </SelectedInfoText>
                <SelectedInfoText>
                  Attribute: {attributes.find((attr) => attr.key === selectedAttribute)?.name}
                </SelectedInfoText>
              </SelectedInfo>
            )}

            {selectedPlayer && (
              <AssistantMessage>
                <AssistantMessageText>{getAssistantMessage(selectedPlayer)}</AssistantMessageText>
              </AssistantMessage>
            )}
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={handleClose}>
      <ModalContainer key={'trainingModalContainer'}>
        <ModalContent>
          <ModalTitle>{getStepTitle()}</ModalTitle>
          <StepIndicator>
            <StepDot active={step === 'player'} />
            <StepDot active={step === 'attribute'} />
            <StepDot active={step === 'confirm'} />
          </StepIndicator>

          {renderStepContent()}
          <ButtonContainer>
            {step !== 'player' && (
              <Button onPress={handleBack}>
                <ButtonText>Back</ButtonText>
              </Button>
            )}
            <Button onPress={handleClose}>
              <ButtonText>Cancel</ButtonText>
            </Button>
            {step === 'confirm' && (
              <Button onPress={handleAssign}>
                <ButtonText>Assign</ButtonText>
              </Button>
            )}
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};

export default TrainingModal;
