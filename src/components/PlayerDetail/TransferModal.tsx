import React from 'react';
import { Modal, TextInput } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { TransferListPlayer } from '../../hooks/useTransferListPlayers';
import { PlayerLike, formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface TransferModalProps {
  visible: boolean;
  player: PlayerLike;
  isAuctionPlayer: boolean;
  transferListPlayer?: TransferListPlayer;
  offerAmount: string;
  isSubmitting: boolean;
  maxBid: number | null;
  isHighestBidder: boolean;
  currentBid: number;
  timeRemaining: string;
  formattedValue: string;
  onClose: () => void;
  onOfferAmountChange: (amount: string) => void;
  onSubmit: () => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: left;
  flex: 1;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-family: 'Nunito';
  font-size: 16px;
  margin-bottom: 8px;
`;

const Input = styled(TextInput)`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 12px;
  border-radius: 8px;
  font-family: 'Nunito';
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  flex: 1;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

export const TransferModal: React.FC<TransferModalProps> = ({
  visible,
  player,
  isAuctionPlayer,
  transferListPlayer,
  offerAmount,
  isSubmitting,
  maxBid,
  isHighestBidder,
  currentBid,
  timeRemaining,
  formattedValue,
  onClose,
  onOfferAmountChange,
  onSubmit,
}) => {
  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitle>
            {player.teamId === '' ? 'Submit Bid' : 'Submit Transfer Offer'}
          </ModalTitle>

          <InputContainer>
            <Label>
              Player: {player.firstName} {player.surname}
            </Label>

            {isAuctionPlayer && transferListPlayer ? (
              <>
                <Label>Player Value: {formattedValue}</Label>
                <Label>
                  Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
                </Label>
                {maxBid && (
                  <Label
                    style={{
                      color: isHighestBidder ? '#2E7D32' : '#e3172a',
                      fontFamily: 'NunitoBold',
                    }}
                  >
                    {isHighestBidder
                      ? '✓ You are the highest bidder!'
                      : '✗ You are not the highest bidder'}
                  </Label>
                )}
                <Label>Time Remaining: {timeRemaining}</Label>
                <Label>Minimum Bid: {formatPlayerValue((maxBid || currentBid) + 1000)}</Label>
                <Label>Your Bid:</Label>
              </>
            ) : (
              <>
                <Label>Current Value: {formattedValue}</Label>
                <Label>Offer Amount:</Label>
              </>
            )}

            <Input
              value={offerAmount}
              onChangeText={onOfferAmountChange}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
          </InputContainer>

          <ButtonContainer>
            <TransferButton
              onPress={onClose}
              style={{ backgroundColor: '#888' }}
            >
              <ButtonText>Cancel</ButtonText>
            </TransferButton>
            <TransferButton onPress={onSubmit} disabled={isSubmitting}>
              <ButtonText>
                {isSubmitting
                  ? 'Submitting...'
                  : isAuctionPlayer
                    ? 'Submit Bid'
                    : 'Submit Offer'}
              </ButtonText>
            </TransferButton>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
