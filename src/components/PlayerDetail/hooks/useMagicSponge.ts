import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useDataCache } from '../../../context/DataCacheContext';
import { useManager } from '../../../context/ManagerContext';
import { PlayerLike } from '../../../utils/PlayerUtils';

export interface MagicSpongeState {
  isSpongeModalVisible: boolean;
  isSpongeLoading: boolean;
}

export interface MagicSpongeActions {
  setIsSpongeModalVisible: (visible: boolean) => void;
  useMagicSponge: (player: PlayerLike, onError: (title: string, message: string) => void) => Promise<void>;
}

export const useMagicSponge = (): [MagicSpongeState, MagicSpongeActions] => {
  const { manager, updateManager } = useManager();
  const { updatePlayer } = useDataCache();
  const [isSpongeModalVisible, setIsSpongeModalVisible] = useState(false);
  const [isSpongeLoading, setIsSpongeLoading] = useState(false);

  const useMagicSpongeApi = async (gameworldId: string, playerId: string) => {
    return callApi(`/${gameworldId}/players/${playerId}/magic-sponge`, {
      method: 'POST',
      body: JSON.stringify({}),
    });
  };

  const handleUseMagicSponge = async (player: PlayerLike, onError: (title: string, message: string) => void) => {
    setIsSpongeLoading(true);
    try {
      const updatedPlayer = await useMagicSpongeApi(player.gameworldId, player.playerId);
      if (updatedPlayer) {
        // Update the player in the cache
        updatePlayer({
          playerId: player.playerId,
          energy: updatedPlayer.energy,
          injuredUntil: updatedPlayer.injuredUntil,
        });

        // Update manager's magic sponges count
        if (manager) {
          updateManager({
            magicSponges: Math.max(0, manager.magicSponges - 1),
          });
        }

        // Also update the local player object for immediate UI update
        player.energy = updatedPlayer.energy;
        player.injuredUntil = updatedPlayer.injuredUntil;
      }
      setIsSpongeModalVisible(false);
    } catch {
      onError('Error', 'Failed to use magic sponge.');
    } finally {
      setIsSpongeLoading(false);
    }
  };

  const state: MagicSpongeState = {
    isSpongeModalVisible,
    isSpongeLoading,
  };

  const actions: MagicSpongeActions = {
    setIsSpongeModalVisible,
    useMagicSponge: handleUseMagicSponge,
  };

  return [state, actions];
};
