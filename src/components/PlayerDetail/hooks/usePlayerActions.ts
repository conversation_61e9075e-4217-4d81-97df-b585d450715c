import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useDataCache } from '../../../context/DataCacheContext';
import { useManager } from '../../../context/ManagerContext';
import { TransferListPlayer } from '../../../hooks/useTransferListPlayers';
import { PlayerLike, formatPlayerValue } from '../../../utils/PlayerUtils';
import { logger } from '../../../utils/logger';

export interface AlertMessage {
  title: string;
  message: string;
}

export interface PlayerActionsState {
  isOfferModalVisible: boolean;
  offerAmount: string;
  isSubmitting: boolean;
  showAlert: boolean;
  alertMessage: AlertMessage;
  maxBid: number | null;
  isHighestBidder: boolean;
  isReleaseModalVisible: boolean;
  isReleasing: boolean;
}

export interface PlayerActionsActions {
  setIsOfferModalVisible: (visible: boolean) => void;
  setOfferAmount: (amount: string) => void;
  setShowAlert: (show: boolean) => void;
  setIsReleaseModalVisible: (visible: boolean) => void;
  handleSubmitOffer: (player: PlayerLike, isAuctionPlayer: boolean, currentBid: number, transferListPlayer?: TransferListPlayer) => Promise<void>;
  handleReleasePlayer: (player: PlayerLike, onClose: () => void) => Promise<void>;
  prefillOfferAmount: (player: PlayerLike, isAuctionPlayer: boolean, currentBid: number) => void;
}

export const usePlayerActions = (): [PlayerActionsState, PlayerActionsActions] => {
  const { manager, team: userTeam, updateTeam } = useManager();
  const { updatePlayer } = useDataCache();
  
  const [isOfferModalVisible, setIsOfferModalVisible] = useState(false);
  const [offerAmount, setOfferAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState<AlertMessage>({ title: '', message: '' });
  const [maxBid, setMaxBid] = useState<number | null>(null);
  const [isHighestBidder, setIsHighestBidder] = useState<boolean>(false);
  const [isReleaseModalVisible, setIsReleaseModalVisible] = useState(false);
  const [isReleasing, setIsReleasing] = useState(false);

  const showAlertMessage = (title: string, message: string) => {
    setAlertMessage({ title, message });
    setShowAlert(true);
  };

  const handleSubmitOffer = async (
    player: PlayerLike, 
    isAuctionPlayer: boolean, 
    currentBid: number, 
    transferListPlayer?: TransferListPlayer
  ) => {
    if (!offerAmount || isNaN(Number(offerAmount))) {
      showAlertMessage('Invalid Amount', 'Please enter a valid transfer amount.');
      return;
    }

    // For auction players, validate bid amount
    if (isAuctionPlayer && transferListPlayer) {
      const bidAmount = Number(offerAmount);
      const minBidRequired = currentBid + 1000;

      if (bidAmount < minBidRequired) {
        showAlertMessage(
          'Bid Too Low',
          `Your bid must be at least ${formatPlayerValue(minBidRequired)} (current bid + £1K).`
        );
        return;
      }

      // Check if auction has ended
      if (transferListPlayer.auctionEndTime < Date.now()) {
        showAlertMessage('Auction Ended', 'This auction has already ended.');
        return;
      }
    }

    try {
      setIsSubmitting(true);

      if (isAuctionPlayer) {
        // Submit bid for auction player
        const bidResponse = await callApi(`/transfer/bid`, {
          method: 'POST',
          body: JSON.stringify({
            player: player.playerId,
            maxBid: Number(offerAmount),
            myTeam: manager?.team?.teamId,
          }),
        });

        // Process the response to get maxBid and highestBidder status
        setMaxBid(bidResponse.maxBid);
        setIsHighestBidder(bidResponse.highestBidder);

        // Update the player in cache with new bid information if it's a TransferListPlayer
        if (transferListPlayer) {
          const updatedBidHistory = transferListPlayer.bidHistory || [];
          const newBid = {
            teamId: manager?.team?.teamId || '',
            teamName: manager?.team?.teamName || 'Your Team',
            maximumBid: bidResponse.maxBid,
            bidTime: Date.now(),
          };

          updatePlayer({
            playerId: player.playerId,
            bidHistory: [...updatedBidHistory, newBid],
            auctionCurrentPrice: Math.max(
              transferListPlayer.auctionCurrentPrice,
              bidResponse.maxBid
            ),
          });
        }

        setIsOfferModalVisible(false);
        showAlertMessage(
          'Success',
          `Bid submitted successfully! ${bidResponse.highestBidder ? 'You are the highest bidder!' : 'You are not the highest bidder.'}`
        );
      } else {
        // Submit transfer offer for team player
        await callApi(`/transfer/offer`, {
          method: 'POST',
          body: JSON.stringify({
            player: player.playerId,
            offer: Number(offerAmount),
            theirTeam: player.teamId,
            myTeam: manager?.team?.teamId,
          }),
        });

        setIsOfferModalVisible(false);
        showAlertMessage('Success', 'Transfer offer submitted successfully!');
      }
    } catch (error) {
      logger.error('Error submitting transfer offer/bid:', error);
      showAlertMessage(
        'Error',
        `Failed to submit ${isAuctionPlayer ? 'bid' : 'transfer offer'}. Please try again.`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReleasePlayer = async (player: PlayerLike, onClose: () => void) => {
    if (!userTeam || !manager) {
      showAlertMessage('Error', 'Unable to release player. Team information not available.');
      return;
    }

    setIsReleasing(true);
    try {
      const response = await callApi('/transfer/release-player', {
        method: 'POST',
        body: JSON.stringify({
          playerId: player.playerId,
          teamId: userTeam.teamId,
        }),
      });

      // Update the team in cache by removing the player
      if (userTeam) {
        const updatedPlayers = userTeam.players.filter((p) => p.playerId !== player.playerId);
        updateTeam({
          players: updatedPlayers,
        });
      }

      setIsReleaseModalVisible(false);
      showAlertMessage(
        'Success',
        response.message || `${player.firstName} ${player.surname} has been released successfully!`
      );

      // Close the player detail view after successful release
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      logger.error('Error releasing player:', error);
      setIsReleaseModalVisible(false);
      showAlertMessage('Error', 'Failed to release player. Please try again.');
    } finally {
      setIsReleasing(false);
    }
  };

  const prefillOfferAmount = (player: PlayerLike, isAuctionPlayer: boolean, currentBid: number) => {
    if (isAuctionPlayer && currentBid) {
      // For auctions, prefill with minimum bid (current bid + 1000)
      setOfferAmount((currentBid + 1000).toString());
    } else {
      // For transfers, prefill with player value
      setOfferAmount(player.value.toFixed(0).toString());
    }
  };

  const state: PlayerActionsState = {
    isOfferModalVisible,
    offerAmount,
    isSubmitting,
    showAlert,
    alertMessage,
    maxBid,
    isHighestBidder,
    isReleaseModalVisible,
    isReleasing,
  };

  const actions: PlayerActionsActions = {
    setIsOfferModalVisible,
    setOfferAmount,
    setShowAlert,
    setIsReleaseModalVisible,
    handleSubmitOffer,
    handleReleasePlayer,
    prefillOfferAmount,
  };

  return [state, actions];
};
