import { useMemo } from 'react';
import { PlayerLike, calculateCurrentEnergy, calculateEnergyByNextMatch, formatPlayerValue } from '../../../utils/PlayerUtils';
import { Team } from '../../../models/team';

export interface PlayerStatus {
  formattedValue: string;
  currentEnergy: number;
  isInjured: boolean;
  isSuspended: boolean;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
  energyByNextMatch?: number;
}

export const usePlayerStatus = (player: PlayerLike, userTeam?: Team, team?: Team): PlayerStatus => {
  return useMemo(() => {
    const formattedValue = formatPlayerValue(player.value);
    const now = Date.now();
    const isInjured = player.injuredUntil && player.injuredUntil > now;
    const isSuspended = player.suspendedForGames > 0;
    const isPlayerInUserTeam = userTeam && player.teamId === userTeam.teamId;
    const isAuctionPlayer = player.teamId === '';
    
    const currentEnergy = calculateCurrentEnergy(
      player.attributes.stamina,
      player.energy,
      player.lastMatchPlayed
    );

    const energyByNextMatch = team?.nextFixture 
      ? calculateEnergyByNextMatch(
          player.attributes.stamina, 
          player.energy, 
          player.lastMatchPlayed, 
          team.nextFixture.date
        )
      : undefined;

    return {
      formattedValue,
      currentEnergy,
      isInjured: !!isInjured,
      isSuspended: !!isSuspended,
      isPlayerInUserTeam: !!isPlayerInUserTeam,
      isAuctionPlayer,
      energyByNextMatch,
    };
  }, [player, userTeam, team]);
};
