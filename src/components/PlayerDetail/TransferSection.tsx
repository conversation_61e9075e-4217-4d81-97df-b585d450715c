import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { TransferListPlayer } from '../../hooks/useTransferListPlayers';
import { PlayerLike, formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';
import { BidHistory } from './BidHistory';
import { AuctionTimerState } from './hooks/useAuctionTimer';

interface StyledProps {
  theme: DefaultTheme;
}

interface TransferSectionProps {
  player: PlayerLike;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
  transferListPlayer?: TransferListPlayer;
  auctionTimer: AuctionTimerState;
  maxBid: number | null;
  isHighestBidder: boolean;
  currentBid: number;
  onTransferPress: () => void;
  onReleasePress: () => void;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 24px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

export const TransferSection: React.FC<TransferSectionProps> = ({
  player,
  isPlayerInUserTeam,
  isAuctionPlayer,
  transferListPlayer,
  auctionTimer,
  maxBid,
  isHighestBidder,
  currentBid,
  onTransferPress,
  onReleasePress,
}) => {
  const { timeRemaining, auctionCompleted } = auctionTimer;
  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';

  if (isPlayerInUserTeam) {
    return (
      <TransferButton
        onPress={onReleasePress}
        style={{ backgroundColor: '#e3172a' }}
      >
        <ButtonText>Release Player</ButtonText>
      </TransferButton>
    );
  }

  return (
    <>
      {isAuctionPlayer && transferListPlayer && (
        <StatusContainer style={{ backgroundColor: '#2E7D32' }}>
          <StatusText>
            Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
          </StatusText>
          <StatusText>{timeRemaining}</StatusText>
          {maxBid && (
            <StatusText
              style={{
                color: isHighestBidder ? '#FFEB3B' : '#FFFFFF',
                fontFamily: 'NunitoBold',
              }}
            >
              {isHighestBidder
                ? '✓ You are the highest bidder!'
                : '✗ You are not the highest bidder'}
            </StatusText>
          )}
        </StatusContainer>
      )}
      
      {/* Only show Bid/Offer button if auction is not completed */}
      {!(isAuctionPlayer && auctionCompleted) && (
        <TransferButton onPress={onTransferPress}>
          <ButtonText>
            {player.teamId === '' ? 'Bid Now' : 'Submit Transfer Offer'}
          </ButtonText>
        </TransferButton>
      )}

      {/* Bid History Section */}
      {isAuctionPlayer && transferListPlayer && (
        <BidHistory transferListPlayer={transferListPlayer} />
      )}
    </>
  );
};
