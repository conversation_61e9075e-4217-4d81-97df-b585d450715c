import React, { useEffect } from 'react';
import { BannerAdSize, BannerAd as MobileBannerAd, TestIds } from 'react-native-google-mobile-ads';
import { logger } from '../../utils/logger';
import { StyledSafeAreaView } from './StyledSafeAreaView';

import { initAds } from './initAds';

// Use test ad unit ID in development, and real ad unit ID in production
const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-3959534729713487/1389516945';

export const BannerAd: React.FC = () => {
  useEffect(() => {
    initAds();
  }, []);

  return (
    <StyledSafeAreaView edges={['bottom']}>
      <MobileBannerAd
        unitId={adUnitId}
        size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
        onAdLoaded={() => {
          logger.log('Ad loaded');
        }}
        onAdFailedToLoad={(error) => {
          logger.error('Ad failed to load: ', error);
        }}
      />
    </StyledSafeAreaView>
  );
};

export default BannerAd;
