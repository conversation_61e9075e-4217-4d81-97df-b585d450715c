import { MaterialIcons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { DraggablePlayerList } from '../components/DraggablePlayerList';
import PlayerDetailView from '../components/PlayerDetailView';
import PositionFilter, {
  PositionFilter as PositionFilterType,
} from '../components/PositionFilter/PositionFilter';
import SavingIndicator from '../components/SavingIndicator';
import { useManager } from '../context/ManagerContext';
import { useTeamInputMethod } from '../context/TeamInputMethodContext';
import { useTeam } from '../hooks/useQueries';
import { useUpdatePlayerOrder } from '../hooks/useUpdatePlayerOrder';
import { Player } from '../models/player';
import { PlayerLike } from '../utils/PlayerUtils';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const WarningBanner = styled.View`
  background-color: #f44336;
  padding: 10px 15px;
  margin: 10px 15px 5px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
`;

const WarningText = styled.Text`
  color: white;
  font-family: 'NunitoBold';
  font-size: 14px;
  flex: 1;
  margin-left: 10px;
`;

const TeamScreen = () => {
  const { team, loading, manager } = useManager();
  const { inputMethod } = useTeamInputMethod();
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [players, setPlayers] = useState(team?.players || []);
  const [positionFilter, setPositionFilter] = useState<PositionFilterType>('All');
  const [savedScrollPosition, setSavedScrollPosition] = useState(0);
  const { updatePlayerOrder, isSaving } = useUpdatePlayerOrder();

  // Fetch team data to calculate averages
  const { data: teamData } = useTeam(manager?.gameworldId, manager?.team?.teamId);

  // Calculate team averages for each position
  const [teamAverages, setTeamAverages] = useState<
    Record<PositionFilterType, Record<string, number>>
  >({
    All: {},
    Goalkeeper: {},
    Defender: {},
    Midfielder: {},
    Attacker: {},
  });

  // Function to handle player selection - defined before conditional returns
  const handlePlayerSelect = useCallback((player: Player, currentScrollPosition?: number) => {
    logger.log('TeamScreen: Player selected, scroll position received:', currentScrollPosition);
    // Save current scroll position before navigating
    if (currentScrollPosition !== undefined) {
      logger.log('TeamScreen: Saving scroll position:', currentScrollPosition);
      setSavedScrollPosition(currentScrollPosition);
    }
    setSelectedPlayer(player);
  }, []);

  // Function to handle player reordering with API update - defined before conditional returns
  const handleReorder = useCallback(
    (newPlayers: Player[]) => {
      setPlayers(newPlayers);
      updatePlayerOrder(newPlayers);
    },
    [updatePlayerOrder]
  );

  // Function to handle position filter selection
  const handlePositionSelect = useCallback((position: PositionFilterType) => {
    setPositionFilter(position);
  }, []);

  // Function to check if any of the first 11 players are unavailable
  const hasUnavailableFirstTeamPlayers = useCallback(() => {
    // Get the first 11 players
    const firstTeam = players.slice(0, 11);

    // Check if any of them are unavailable (injured or suspended)
    return firstTeam.some(
      (player) =>
        (player.injuredUntil && player.injuredUntil > Date.now()) || player.suspendedForGames > 0
    );
  }, [players]);

  // Update players when team changes
  useEffect(() => {
    setPlayers(team?.players || []);
  }, [team]);

  // Calculate team averages when team data changes
  useEffect(() => {
    if (teamData?.players) {
      const calculatePositionAverages = (playerList: PlayerLike[], attributes: string[]) => {
        const result: Record<string, number> = {};

        attributes.forEach((attr) => {
          const values = playerList.map(
            (player) => player.attributes[attr as keyof typeof player.attributes] || 0
          );
          const sum = values.reduce((acc, val) => acc + val, 0);
          result[attr] = values.length > 0 ? sum / values.length : 0;
        });

        return result;
      };

      const averages: Record<PositionFilterType, Record<string, number>> = {
        All: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
          'tackling',
          'marking',
          'heading',
          'passing',
          'vision',
          'ballControl',
          'finishing',
          'pace',
          'crossing',
        ]),
        Goalkeeper: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
        ]),
        Defender: calculatePositionAverages(teamData.players, ['tackling', 'marking', 'heading']),
        Midfielder: calculatePositionAverages(teamData.players, [
          'passing',
          'vision',
          'ballControl',
        ]),
        Attacker: calculatePositionAverages(teamData.players, ['finishing', 'pace', 'crossing']),
      };

      setTeamAverages(averages);
    }
  }, [teamData?.players]);

  // The renderPlayer function is no longer needed as it's handled inside the DraggablePlayerList component
  if (loading) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  // If a player is selected, show the player detail view
  if (selectedPlayer) {
    return (
      <PlayerDetailView
        player={selectedPlayer}
        onClose={() => setSelectedPlayer(null)}
        team={team || undefined}
      />
    );
  }

  // Otherwise, show the team view
  return (
    <Container>
      {hasUnavailableFirstTeamPlayers() && (
        <WarningBanner>
          <MaterialIcons name="warning" size={24} color="white" />
          <WarningText>
            Some of your first team are unavailable. Make sure you move them out of your team before
            the next match
          </WarningText>
        </WarningBanner>
      )}

      <PositionFilter positionFilter={positionFilter} onPositionSelect={handlePositionSelect} />

      <DraggablePlayerList
        data={players}
        onReorder={handleReorder}
        onSelect={handlePlayerSelect}
        positionFilter={positionFilter}
        teamAverages={teamAverages[positionFilter]}
        inputMethod={inputMethod}
        initialScrollPosition={savedScrollPosition}
      />

      <SavingIndicator visible={isSaving} />
    </Container>
  );
};

export default TeamScreen;
