import { useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Image, Modal, Platform, TextInput, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../../api/client';
import ActiveTransferCard from '../../components/ActiveTransferCard';
import { ApiErrorModal } from '../../components/ApiErrorModal';
import { CrossPlatformAlert } from '../../components/CrossPlatformAlert';
import LeagueSelectionModal from '../../components/LeagueSelectionModal';
import PlayerDetailView from '../../components/PlayerDetailView';
import PlayerCard from '../../components/PlayerRow/PlayerCard';
import TeamSelectionModal from '../../components/TeamSelectionModal';
import { Text } from '../../components/Text';
import { useManager } from '../../context/ManagerContext';
import { useApiErrorModal } from '../../hooks/useApiErrorModal';
import { useLeagues } from '../../hooks/useLeagues';
import { ActiveTransfer, useMyActiveTransfers } from '../../hooks/useMyBidsPlayers';
import { ScoutedPlayer, useScoutedPlayers, useTeam } from '../../hooks/useQueries';
import { Player } from '../../models/player';
import { logger } from '../../utils/logger';

// Add PlayerLike type to handle both Player and ScoutedPlayer
type PlayerLike = Player | ScoutedPlayer;

// Helper types for FlatList data
interface ListHeaderItem {
  isHeader: true;
  title: string;
  type: 'myActiveTransfers' | 'scoutedPlayers';
}
interface ListEmptyItem {
  isEmpty: true;
  type: 'myActiveTransfers' | 'scoutedPlayers';
}
type ListItem = ScoutedPlayer | ActiveTransfer | ListHeaderItem | ListEmptyItem;

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
  padding: 16px;
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  gap: 12px;
  margin-bottom: 12px;
`;

const ScoutButton = styled.TouchableOpacity<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  padding: 12px 24px;
  border-radius: 8px;
  flex: 1;
  align-items: center;
  justify-content: center;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

const ButtonText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'Nunito Bold';
  font-size: 16px;
`;

const FilterContainer = styled.View`
  margin-bottom: 16px;
  z-index: 999;
  position: relative;
`;

const DropdownContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 0 12px;
  position: relative;
  z-index: 999;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

const DropdownButton = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
`;

const DropdownText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'Nunito Bold';
  font-size: 16px;
`;

const DropdownOptions = styled.View<StyledProps>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  margin-top: 4px;
  z-index: 999;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

interface OptionItemProps extends StyledProps {
  isSelected?: boolean;
}

const OptionItem = styled.TouchableOpacity<OptionItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '40' : 'transparent'};
`;

interface OptionTextProps extends StyledProps {
  isSelected?: boolean;
}

const OptionText = styled(Text)<OptionTextProps>`
  color: ${(props) =>
    props.isSelected ? props.theme.colors.primary : props.theme.colors.text.primary};
  font-family: ${(props) => (props.isSelected ? 'NunitoBold' : 'Nunito')};
  font-size: 16px;
`;

const ListContainer = styled.View`
  flex: 1;
`;

const ListHeaderText = styled(Text)<StyledProps>`
  font-size: 18px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin: 0 16px 8px 16px;
`;

const EmptyListContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

const EmptyListText = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
  margin-top: 16px;
`;

interface LoadMoreButtonProps extends StyledProps {
  disabled?: boolean;
}

const LoadMoreButton = styled.TouchableOpacity<LoadMoreButtonProps>`
  background-color: ${(props) =>
    props.disabled ? props.theme.colors.surface + '80' : props.theme.colors.surface};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  margin: 16px 0;
  align-self: center;
  min-width: 150px;
`;

// Modal styles
const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const InputContainer = styled.View`
  margin-bottom: 20px;
`;

const Label = styled(Text)`
  font-size: 14px;
  margin-bottom: 8px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const Input = styled(TextInput)`
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border || '#ccc'};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ModalButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
`;

const ActionButton = styled.TouchableOpacity<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    switch (props.variant) {
      case 'danger':
        return '#e3172a';
      case 'secondary':
        return '#888';
      default:
        return props.theme.colors.primary;
    }
  }};
`;

const ActionButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 14px;
  text-align: center;
`;

const TokenCountContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12px;
  gap: 16px;
`;

const TokenTypeContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
`;

const TokenIcon = styled(Image)`
  width: 24px;
  height: 24px;
  margin-right: 4px;
`;

const TokenCountText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  font-size: 16px;
`;

// Position filter options
type PositionFilter = 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';

const SCOUT_COST = 5000;

const ScoutingTab = () => {
  const { manager } = useManager();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isLeagueModalVisible, setLeagueModalVisible] = useState(false);
  const [isTeamModalVisible, setTeamModalVisible] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showScoutingConfirmation, setShowScoutingConfirmation] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorDetails, setErrorDetails] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>(undefined);
  const [scoutedPlayers, setScoutedPlayers] = useState<ScoutedPlayer[]>([]);
  const [myActiveTransfers, setMyActiveTransfers] = useState<ActiveTransfer[]>([]);
  const [selectedPlayer, setSelectedPlayer] = useState<ScoutedPlayer | null>(null);
  const [selectedTransfer, setSelectedTransfer] = useState<ActiveTransfer | null>(null);
  const [negotiatingTransfer, setNegotiatingTransfer] = useState<ActiveTransfer | null>(null);
  const [isNegotiateModalVisible, setIsNegotiateModalVisible] = useState(false);
  const [negotiateAmount, setNegotiateAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [positionFilter, setPositionFilter] = useState<PositionFilter>('All');
  const [showPositionDropdown, setShowPositionDropdown] = useState(false);
  const [reachedEnd, setReachedEnd] = useState(false);
  const [teamAverages, setTeamAverages] = useState<Record<PositionFilter, Record<string, number>>>({
    All: {},
    Goalkeeper: {},
    Defender: {},
    Midfielder: {},
    Attacker: {},
  });
  const [showClientError, setShowClientError] = useState(false);
  const [clientErrorMessage, setClientErrorMessage] = useState<string | null>(null);

  // Reference to the dropdown container for click-outside handling
  const dropdownRef = React.useRef<View | null>(null);

  const {
    data: leagues,
    isLoading: isLoadingLeagues,
    error: leaguesError,
  } = useLeagues(manager?.gameworldId);
  const {
    data: scoutedPlayersData,
    isLoading: isLoadingScoutedPlayers,
    error: scoutedPlayersError,
  } = useScoutedPlayers(manager?.gameworldId, manager?.team?.teamId, nextToken);
  const {
    data: myActiveTransfersData,
    isLoading: isLoadingMyActiveTransfers,
    error: myActiveTransfersError,
  } = useMyActiveTransfers(manager?.gameworldId);
  const {
    data: teamData,
    isLoading: isLoadingTeam,
    error: teamError,
  } = useTeam(manager?.gameworldId, manager?.team?.teamId);

  const { visible, error, dismiss } = useApiErrorModal(
    scoutedPlayersError || myActiveTransfersError || leaguesError || teamError
  );

  const handleScoutLeague = () => {
    setLeagueModalVisible(true);
  };

  const handleLeagueSelect = async (leagueId: string) => {
    if ((manager?.scoutTokens ?? 0) < 1) {
      setClientErrorMessage('You do not have enough scout tokens.');
      setShowClientError(true);
      return;
    }
    if ((manager?.team?.balance ?? 0) < SCOUT_COST) {
      setClientErrorMessage('You do not have enough funds to send a scout.');
      setShowClientError(true);
      return;
    }
    try {
      setIsLoading(true);
      await callApi('/scouting/request', {
        method: 'POST',
        body: JSON.stringify({
          type: 'league',
          id: leagueId,
        }),
      });
      // Decrement scout token and balance on success
      if (manager) {
        manager.scoutTokens = (manager.scoutTokens ?? 1) - 1;
        if (manager.team) manager.team.balance = (manager.team.balance ?? 0) - SCOUT_COST;
      }
      setLeagueModalVisible(false);
      setShowScoutingConfirmation(true);
    } catch (e) {
      setErrorDetails(e);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleScoutTeam = () => {
    setTeamModalVisible(true);
  };

  const handleTeamSelect = async (teamId: string) => {
    if ((manager?.scoutTokens ?? 0) < 1) {
      setClientErrorMessage('You do not have enough scout tokens.');
      setShowClientError(true);
      return;
    }
    if ((manager?.team?.balance ?? 0) < SCOUT_COST) {
      setClientErrorMessage('You do not have enough funds to send a scout.');
      setShowClientError(true);
      return;
    }
    try {
      setIsLoading(true);
      await callApi('/scouting/request', {
        method: 'POST',
        body: JSON.stringify({
          type: 'team',
          id: teamId,
        }),
      });
      // Decrement scout token and balance on success
      if (manager) {
        manager.scoutTokens = (manager.scoutTokens ?? 1) - 1;
        if (manager.team) manager.team.balance = (manager.team.balance ?? 0) - SCOUT_COST;
      }
      setTeamModalVisible(false);
      setShowScoutingConfirmation(true);
    } catch (e) {
      setErrorDetails(e);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect for handling my active transfers data
  useEffect(() => {
    if (myActiveTransfersData) {
      logger.log('My active transfers data received:', myActiveTransfersData);
      setMyActiveTransfers(myActiveTransfersData);
    }
  }, [myActiveTransfersData]);

  // Update scoutedPlayers when data is fetched
  useEffect(() => {
    if (scoutedPlayersData) {
      logger.log('Scouted players data received:', scoutedPlayersData);

      // Check if we've reached the end of the list
      if (isLoadingMore && scoutedPlayersData.scoutedPlayers.length === 0) {
        logger.log('Received empty page, reached end of list');
        setReachedEnd(true);
        setIsLoadingMore(false);
        return;
      }

      if (nextToken && isLoadingMore) {
        // Append new players to existing list with duplicate filtering
        setScoutedPlayers((prev) => {
          if (!Array.isArray(prev)) {
            logger.warn('Previous players is not an array:', prev);
            return scoutedPlayersData.scoutedPlayers;
          }

          // Filter out duplicates before combining
          const existingPlayerIds = new Set(prev.map((player) => player.playerId));
          const uniqueNewPlayers = scoutedPlayersData.scoutedPlayers.filter(
            (player) => !existingPlayerIds.has(player.playerId)
          );

          logger.log('Existing players:', prev.length);
          logger.log('New unique players:', uniqueNewPlayers.length);

          // If we got no new unique players, we've reached the end
          if (uniqueNewPlayers.length === 0 && isLoadingMore) {
            logger.log('No new unique players, reached end of list');
            setReachedEnd(true);
          }

          // Combine previous and unique new players
          const combinedPlayers = [...prev, ...uniqueNewPlayers];
          logger.log('Combined players count:', combinedPlayers.length);
          return combinedPlayers;
        });

        // Reset loading more state
        setIsLoadingMore(false);
      } else {
        // Replace the list with new data for initial load
        setScoutedPlayers(scoutedPlayersData.scoutedPlayers);
        // Reset reached end flag on initial load
        setReachedEnd(false);
      }
    }
  }, [scoutedPlayersData, isLoadingMore, nextToken]);

  const loadMorePlayers = () => {
    // Don't try to load more if we've reached the end
    if (reachedEnd) {
      logger.log('Already reached end of list, not loading more');
      return;
    }

    if (scoutedPlayersData?.pagination?.hasMore && scoutedPlayersData?.pagination?.nextToken) {
      logger.log('Loading more players with token:', scoutedPlayersData.pagination.nextToken);
      setIsLoadingMore(true); // Set loading more state to true
      setNextToken(scoutedPlayersData.pagination.nextToken);
    } else {
      logger.log('No more players to load');
      setReachedEnd(true); // Mark that we've reached the end
    }
  };

  // Calculate team average attributes for each position category
  useEffect(() => {
    if (teamData?.players && teamData.players.length > 0) {
      const calculatePositionAverages = (players: Player[], attributeKeys: string[]) => {
        const sum = attributeKeys.reduce(
          (acc, key) => {
            acc[key] = 0;
            return acc;
          },
          {} as Record<string, number>
        );

        let count = 0;

        players.forEach((player) => {
          attributeKeys.forEach((key) => {
            // @ts-ignore - we know these keys exist in the attributes
            sum[key] += player.attributes[key];
          });
          count++;
        });

        return attributeKeys.reduce(
          (acc, key) => {
            acc[key] = count > 0 ? sum[key] / count : 0;
            return acc;
          },
          {} as Record<string, number>
        );
      };

      const averages: Record<PositionFilter, Record<string, number>> = {
        All: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
          'tackling',
          'marking',
          'heading',
          'passing',
          'vision',
          'ballControl',
          'finishing',
          'pace',
          'crossing',
        ]),
        Goalkeeper: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
        ]),
        Defender: calculatePositionAverages(teamData.players, ['tackling', 'marking', 'heading']),
        Midfielder: calculatePositionAverages(teamData.players, [
          'passing',
          'vision',
          'ballControl',
        ]),
        Attacker: calculatePositionAverages(teamData.players, ['finishing', 'pace', 'crossing']),
      };

      setTeamAverages(averages);
    }
  }, [teamData?.players]);

  // We're not filtering players, just changing what attributes are displayed
  // based on the selected position filter

  // For web platforms, we can add a click-outside handler
  useEffect(() => {
    if (Platform.OS === 'web' && typeof document !== 'undefined') {
      const handleClickOutside = (event: any) => {
        if (
          dropdownRef.current &&
          // @ts-ignore - contains is available in web but not in RN types
          !dropdownRef.current.contains(event.target) &&
          showPositionDropdown
        ) {
          setShowPositionDropdown(false);
        }
      };

      // Add event listener when dropdown is open
      if (showPositionDropdown) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      // Clean up event listener
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showPositionDropdown]);

  // Handle window resize to update dropdown position
  useEffect(() => {
    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      const handleResize = () => {
        // Close dropdown on resize to prevent positioning issues
        if (showPositionDropdown) {
          setShowPositionDropdown(false);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [showPositionDropdown]);

  const togglePositionDropdown = () => {
    setShowPositionDropdown(!showPositionDropdown);
  };

  const handlePositionSelect = (position: PositionFilter) => {
    setPositionFilter(position);
    setShowPositionDropdown(false);
  };

  const handleAcceptCounterOffer = async (transfer: ActiveTransfer) => {
    try {
      setIsLoading(true);
      await callApi(`/transfer/accept`, {
        method: 'POST',
        body: JSON.stringify({
          transferRequestId: transfer.id,
        }),
      });

      // Refresh the active transfers data to show the updated offer
      await queryClient.invalidateQueries({
        queryKey: ['myActiveTransfers', manager?.gameworldId],
      });

      setShowConfirmation(true);
    } catch (err) {
      logger.error('Error accepting counter offer:', err);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle negotiate button press
  const handleNegotiate = (transfer: ActiveTransfer) => {
    // Prefill with the user's last offer amount
    setNegotiateAmount(transfer.value.toString());
    setNegotiatingTransfer(transfer);
    setIsNegotiateModalVisible(true);
  };

  // Handle negotiate submission
  const handleSubmitNegotiation = async () => {
    if (!negotiateAmount || isNaN(Number(negotiateAmount)) || !negotiatingTransfer) {
      setShowError(true);
      return;
    }

    try {
      setIsSubmitting(true);
      await callApi(`/transfer/offer`, {
        method: 'POST',
        body: JSON.stringify({
          player: negotiatingTransfer.player.playerId,
          offer: Number(negotiateAmount),
          theirTeam: negotiatingTransfer.seller.teamId,
          myTeam: manager?.team?.teamId,
        }),
      });

      setIsNegotiateModalVisible(false);
      setNegotiatingTransfer(null);
      setNegotiateAmount('');

      // Refresh the active transfers data to show the updated offer
      queryClient.invalidateQueries({ queryKey: ['myActiveTransfers', manager?.gameworldId] });

      setShowConfirmation(true);
    } catch (err) {
      logger.error('Error submitting negotiation:', err);
      setShowError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelTransfer = async (transfer: ActiveTransfer) => {
    try {
      setIsLoading(true);
      await callApi('/transfer/cancel', {
        method: 'POST',
        body: JSON.stringify({
          transferRequestId: transfer.id,
        }),
      });

      // Refresh the active transfers data to remove the cancelled transfer
      await queryClient.invalidateQueries({
        queryKey: ['myActiveTransfers', manager?.gameworldId],
      });

      setShowConfirmation(true);
    } catch (err) {
      logger.error('Error cancelling transfer:', err);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const renderPlayer = ({ item }: { item: ScoutedPlayer; index: number }) => {
    const handlePlayerSelect = (player: PlayerLike) => {
      setSelectedPlayer(player as ScoutedPlayer);
    };

    return (
      <PlayerCard
        player={item}
        onSelect={handlePlayerSelect}
        isSelected={selectedPlayer?.playerId === item.playerId}
        showValue={true}
        positionFilter={positionFilter}
        teamAverages={teamAverages[positionFilter]}
      />
    );
  };

  const renderEmptyList = () => (
    <EmptyListContainer>
      <EmptyListText>
        No scouted players found. Use the buttons above to scout players.
      </EmptyListText>
    </EmptyListContainer>
  );

  const renderEmptyActiveTransfersList = () => (
    <EmptyListContainer>
      <EmptyListText>You have no active transfer offers.</EmptyListText>
    </EmptyListContainer>
  );

  // Render an active transfer card
  const renderActiveTransfer = ({ item }: { item: ActiveTransfer }) => {
    const handleTransferSelect = (transfer: ActiveTransfer) => {
      setSelectedTransfer(transfer);
    };

    return <ActiveTransferCard transfer={item} onSelect={handleTransferSelect} />;
  };

  // Update loading state based on query loading states
  useEffect(() => {
    logger.log('Loading states:', {
      isLoadingScoutedPlayers,
      isLoadingMyActiveTransfers,
      isLoadingTeam,
      isLoadingLeagues,
      isLoadingMore,
    });

    // Only set isLoading to true for initial load, not for pagination
    if (!isLoadingMore) {
      setIsLoading(
        isLoadingScoutedPlayers || isLoadingMyActiveTransfers || isLoadingTeam || isLoadingLeagues
      );
    }
  }, [
    isLoadingScoutedPlayers,
    isLoadingMyActiveTransfers,
    isLoadingTeam,
    isLoadingLeagues,
    isLoadingMore,
  ]);

  if (isLoading) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  return (
    <Container>
      <TokenCountContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../assets/scoutToken.png')} />
          <TokenCountText>{manager?.scoutTokens ?? 0}</TokenCountText>
        </TokenTypeContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../assets/superScoutToken.png')} />
          <TokenCountText>{manager?.superScoutTokens ?? 0}</TokenCountText>
        </TokenTypeContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../assets/quid.svg')} />
          <TokenCountText>{manager?.team?.balance?.toLocaleString() ?? '0'}</TokenCountText>
        </TokenTypeContainer>
      </TokenCountContainer>

      <ButtonContainer>
        <ScoutButton onPress={handleScoutLeague}>
          <ButtonText>Scout League</ButtonText>
        </ScoutButton>
        <ScoutButton onPress={handleScoutTeam}>
          <ButtonText>Scout Team</ButtonText>
        </ScoutButton>
      </ButtonContainer>

      <FilterContainer>
        <DropdownContainer ref={dropdownRef}>
          <DropdownButton onPress={togglePositionDropdown}>
            <DropdownText>Show Attributes: {positionFilter}</DropdownText>
            <DropdownText>{showPositionDropdown ? '▲' : '▼'}</DropdownText>
          </DropdownButton>
          {showPositionDropdown && (
            <DropdownOptions>
              {(
                ['All', 'Goalkeeper', 'Defender', 'Midfielder', 'Attacker'] as PositionFilter[]
              ).map((position) => (
                <OptionItem
                  key={position}
                  onPress={() => handlePositionSelect(position)}
                  isSelected={positionFilter === position}
                >
                  <OptionText isSelected={positionFilter === position}>{position}</OptionText>
                </OptionItem>
              ))}
            </DropdownOptions>
          )}
        </DropdownContainer>
      </FilterContainer>

      <ListContainer>
        <FlatList<ListItem>
          data={(() => {
            const combinedData: ListItem[] = [];
            if (myActiveTransfers.length > 0) {
              combinedData.push({
                isHeader: true,
                title: 'Your Active Transfer Offers',
                type: 'myActiveTransfers',
              });
              combinedData.push(...myActiveTransfers);
            }
            combinedData.push({ isHeader: true, title: 'Scouted Players', type: 'scoutedPlayers' });
            if (scoutedPlayers.length > 0) {
              combinedData.push(...scoutedPlayers);
            } else {
              combinedData.push({ isEmpty: true, type: 'scoutedPlayers' });
            }
            return combinedData;
          })()}
          renderItem={({ item }) => {
            if ('isHeader' in item && item.isHeader) {
              return (
                <ListHeaderText style={{ marginTop: 16, marginBottom: 8 }}>
                  {item.title}
                </ListHeaderText>
              );
            }
            if ('isEmpty' in item && item.isEmpty) {
              return item.type === 'myActiveTransfers'
                ? renderEmptyActiveTransfersList()
                : renderEmptyList();
            }
            if ('id' in item && 'player' in item && 'seller' in item) {
              return renderActiveTransfer({ item: item as ActiveTransfer });
            }
            return renderPlayer({ item: item as ScoutedPlayer, index: 0 });
          }}
          keyExtractor={(item, index) => {
            if ('isHeader' in item && item.isHeader) return `${item.type}-header-${index}`;
            if ('isEmpty' in item && item.isEmpty) return `${item.type}-empty-${index}`;
            if ('id' in item && 'player' in item && 'seller' in item) {
              return `transfer-${(item as ActiveTransfer).id}`;
            }
            return (item as ScoutedPlayer).playerId;
          }}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListFooterComponent={() =>
            scoutedPlayersData?.pagination?.hasMore && !reachedEnd ? (
              <LoadMoreButton onPress={loadMorePlayers} disabled={isLoadingMore}>
                {isLoadingMore ? (
                  <>
                    <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
                    <ButtonText>Loading...</ButtonText>
                  </>
                ) : (
                  <ButtonText>Load More</ButtonText>
                )}
              </LoadMoreButton>
            ) : null
          }
        />
      </ListContainer>

      {/* Player detail view rendered outside of ListContainer for proper z-index handling */}
      {selectedPlayer && (
        <PlayerDetailView player={selectedPlayer} onClose={() => setSelectedPlayer(null)} />
      )}

      {/* Active transfer detail view */}
      {selectedTransfer && (
        <Modal
          visible={true}
          transparent
          animationType="fade"
          onRequestClose={() => setSelectedTransfer(null)}
        >
          <ModalContainer>
            <ModalContent>
              <ModalTitle>Transfer Details</ModalTitle>

              <InputContainer>
                <Label>
                  Player: {selectedTransfer.player.firstName} {selectedTransfer.player.surname}
                </Label>
                <Label>Your Offer: £{selectedTransfer.value.toLocaleString()}</Label>
                <Label>To: {selectedTransfer.seller.teamName}</Label>
                {selectedTransfer.counterOfferValue !== '0' && (
                  <Label style={{ color: '#e3172a', fontFamily: 'NunitoBold' }}>
                    Counter Offer: £{Number(selectedTransfer.counterOfferValue).toLocaleString()}
                  </Label>
                )}
              </InputContainer>

              {/* Show action buttons based on transfer state */}
              {selectedTransfer.buyer === manager?.team?.teamId ? (
                // Check if there's a valid counter offer (newer than original offer)
                selectedTransfer.counterOfferValue !== '0' &&
                selectedTransfer.counterOfferTime !== '0' &&
                Number(selectedTransfer.counterOfferTime) > selectedTransfer.date ? (
                  // Show counter offer response buttons
                  <ModalButtonContainer>
                    <ActionButton
                      variant="primary"
                      onPress={() => {
                        handleAcceptCounterOffer(selectedTransfer);
                        setSelectedTransfer(null);
                      }}
                    >
                      <ActionButtonText>Accept</ActionButtonText>
                    </ActionButton>
                    <ActionButton
                      variant="secondary"
                      onPress={() => {
                        handleNegotiate(selectedTransfer);
                        setSelectedTransfer(null);
                      }}
                    >
                      <ActionButtonText>Negotiate</ActionButtonText>
                    </ActionButton>
                    <ActionButton
                      variant="danger"
                      onPress={() => {
                        handleCancelTransfer(selectedTransfer);
                        setSelectedTransfer(null);
                      }}
                    >
                      <ActionButtonText>You're having a laugh</ActionButtonText>
                    </ActionButton>
                  </ModalButtonContainer>
                ) : (
                  // Show amend/withdraw buttons when no counter offer
                  <ModalButtonContainer>
                    <ActionButton
                      variant="primary"
                      onPress={() => {
                        handleNegotiate(selectedTransfer);
                        setSelectedTransfer(null);
                      }}
                    >
                      <ActionButtonText>Amend Offer</ActionButtonText>
                    </ActionButton>
                    <ActionButton
                      variant="danger"
                      onPress={() => {
                        handleCancelTransfer(selectedTransfer);
                        setSelectedTransfer(null);
                      }}
                    >
                      <ActionButtonText>Withdraw Offer</ActionButtonText>
                    </ActionButton>
                  </ModalButtonContainer>
                )
              ) : (
                // Not the buyer, just show close button
                <ModalButtonContainer>
                  <ActionButton variant="secondary" onPress={() => setSelectedTransfer(null)}>
                    <ActionButtonText>Close</ActionButtonText>
                  </ActionButton>
                </ModalButtonContainer>
              )}
            </ModalContent>
          </ModalContainer>
        </Modal>
      )}

      {/* Negotiate Modal */}
      <Modal
        visible={isNegotiateModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsNegotiateModalVisible(false)}
      >
        <ModalContainer>
          <ModalContent>
            <ModalTitle>Update Transfer Offer</ModalTitle>

            <InputContainer>
              {negotiatingTransfer && (
                <>
                  <Label>
                    Player: {negotiatingTransfer.player.firstName}{' '}
                    {negotiatingTransfer.player.surname}
                  </Label>
                  <Label>Player Value: £{negotiatingTransfer.player.value.toLocaleString()}</Label>
                  <Label>Your Last Offer: £{negotiatingTransfer.value.toLocaleString()}</Label>
                  {negotiatingTransfer.counterOfferValue !== '0' && (
                    <Label>
                      Their Counter Offer: £
                      {Number(negotiatingTransfer.counterOfferValue).toLocaleString()}
                    </Label>
                  )}
                  <Label>New Offer Amount:</Label>
                </>
              )}

              <Input
                value={negotiateAmount}
                onChangeText={setNegotiateAmount}
                placeholder="Enter amount"
                keyboardType="numeric"
              />
            </InputContainer>

            <ModalButtonContainer>
              <ActionButton
                variant="secondary"
                onPress={() => {
                  setIsNegotiateModalVisible(false);
                  setNegotiateAmount('');
                  setNegotiatingTransfer(null);
                }}
              >
                <ActionButtonText>Cancel</ActionButtonText>
              </ActionButton>
              <ActionButton
                variant="primary"
                onPress={handleSubmitNegotiation}
                disabled={isSubmitting}
              >
                <ActionButtonText>
                  {isSubmitting ? 'Submitting...' : 'Submit Offer!'}
                </ActionButtonText>
              </ActionButton>
            </ModalButtonContainer>
          </ModalContent>
        </ModalContainer>
      </Modal>

      <LeagueSelectionModal
        visible={isLeagueModalVisible}
        leagues={leagues || []}
        onClose={() => setLeagueModalVisible(false)}
        onSelect={handleLeagueSelect}
      />

      <TeamSelectionModal
        visible={isTeamModalVisible}
        leagues={leagues || []}
        onClose={() => setTeamModalVisible(false)}
        onSelect={handleTeamSelect}
      />

      <CrossPlatformAlert
        visible={showConfirmation}
        title="Success"
        message="Transfer offer submitted successfully"
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowConfirmation(false),
          },
        ]}
        onDismiss={() => setShowConfirmation(false)}
      />

      <CrossPlatformAlert
        visible={showScoutingConfirmation}
        title="Success"
        message="Your scout has packed his bags and is off to look for players who know their left from their right."
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowScoutingConfirmation(false),
          },
        ]}
        onDismiss={() => setShowScoutingConfirmation(false)}
      />

      <CrossPlatformAlert
        visible={showError}
        title="Error"
        message="Failed to send scouting request. Please try again."
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowError(false),
          },
        ]}
        onDismiss={() => setShowError(false)}
      />

      <CrossPlatformAlert
        visible={showClientError}
        title="Error"
        message={clientErrorMessage ?? ''}
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowClientError(false),
          },
        ]}
        onDismiss={() => setShowClientError(false)}
      />

      <ApiErrorModal
        visible={showError}
        error={errorDetails}
        onDismiss={() => setShowError(false)}
      />
    </Container>
  );
};

export default ScoutingTab;
