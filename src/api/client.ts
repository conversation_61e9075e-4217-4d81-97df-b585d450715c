import { fetchAuthSession } from 'aws-amplify/auth';
import axios, { AxiosRequestConfig } from 'axios';
import { logger } from '../utils/logger';

export async function callApi(endpoint: string, options: RequestInit = {}, skipAuth = false) {
  try {
    const session = await fetchAuthSession();
    const authHeaders: {
      Authorization?: string;
    } = {};
    if (!skipAuth && process.env.EXPO_PUBLIC_IS_LOCAL !== 'true') {
      const token = session.tokens?.accessToken?.toString();
      authHeaders['Authorization'] = `Bearer ${token}`;
    }

    // Convert fetch options to axios config
    const axiosConfig: AxiosRequestConfig = {
      method: options.method || 'GET',
      // @ts-ignore
      headers: {
        ...options.headers,
        'Content-Type': 'application/json',
        ...authHeaders,
      },
    };

    // Handle request body
    if ((options.method === 'POST' || options.method === 'PUT') && options.body) {
      if (typeof options.body !== 'string') {
        logger.log('Converting body to JSON:', options.body);
        axiosConfig.data = options.body;
      } else {
        axiosConfig.data = JSON.parse(options.body);
      }
    }

    const response = await axios(`${process.env.EXPO_PUBLIC_API_URL}${endpoint}`, axiosConfig);

    // Axios automatically throws for non-2xx responses, so we don't need to check response.ok
    return { ...response.data, status: response.status };
  } catch (error) {
    logger.error('API call failed:', error);
    throw error;
  }
}
